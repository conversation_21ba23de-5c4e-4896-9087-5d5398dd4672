# Dependencies
node_modules/
jspm_packages/

# Production builds
.next/
out/
build/
dist/
dist-ssr/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# Database
*.db
*.sqlite
*.sqlite3

# Prisma
prisma/migrations/
.env.example

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# TypeScript
*.tsbuildinfo
next-env.d.ts

# Cache directories
.npm
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*
.eslintcache
.stylelintcache
.cache
.parcel-cache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Snowpack dependency directory (https://snowpack.dev/)
web_modules/

# Temporary files
*.tmp
*.temp
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini

# Windows
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Supabase
.supabase/

# Testing
coverage/
.nyc_output/
test-results/
playwright-report/

# Storybook
storybook-static/

# Vercel
.vercel

# Turbo
.turbo
