# Migration Guide: Supa<PERSON> Auth → Better-Auth

This document outlines the complete migration from Supabase authentication to Better-Auth.

## Overview

The migration replaces Supabase's authentication system with Better-Auth, a more flexible and framework-agnostic solution that provides better TypeScript support and easier customization.

## Changes Made

### 1. Dependencies
**Removed:**
- `@supabase/ssr`
- `@supabase/supabase-js`

**Added:**
- `better-auth`
- `pg` (PostgreSQL client)
- `@types/pg`

### 2. Configuration Files

#### New Files Created:
- `src/lib/auth.ts` - Server-side Better-Auth configuration
- `src/lib/auth-client.ts` - Client-side Better-Auth configuration
- `src/app/api/auth/[...all]/route.ts` - API route handler
- `src/components/auth-provider.tsx` - React context provider
- `src/middleware.ts` - Route protection middleware

#### Files Removed:
- `src/lib/supabase.ts`
- `src/lib/supabase-server.ts`

### 3. Environment Variables

**Removed:**
```env
NEXT_PUBLIC_SUPABASE_URL
NEXT_PUBLIC_SUPABASE_ANON_KEY
SUPABASE_SERVICE_ROLE_KEY
NEXTAUTH_URL
NEXTAUTH_SECRET
```

**Added:**
```env
BETTER_AUTH_SECRET
BETTER_AUTH_URL
```

### 4. Code Changes

#### Authentication Pages:
- Updated login page to use `signIn.email()`
- Updated register page to use `signUp.email()`
- Improved error handling for Better-Auth response format

#### Protected Routes:
- Updated dashboard layout to use `auth.api.getSession()`
- Updated home page session checking
- Added middleware for route protection

#### Components:
- Updated header component to use Better-Auth `signOut()`
- Updated user data structure (user.name instead of user.user_metadata.name)

#### API Routes:
- Updated optimization API to use Better-Auth session validation

## Setup Instructions

### 1. Install Dependencies
```bash
npm install better-auth pg @types/pg
```

### 2. Update Environment Variables
Copy the new environment variables from `.env.local`:
```env
BETTER_AUTH_SECRET="your-better-auth-secret-key-replace-with-random-string"
BETTER_AUTH_URL="http://localhost:3001"
```

### 3. Generate Database Schema
```bash
npm run auth:generate
```

### 4. Run Database Migrations
```bash
npm run auth:migrate
```

### 5. Start Development Server
```bash
npm run dev
```

## Key Differences

### Session Structure
**Supabase:**
```typescript
session.user.user_metadata.name
session.user.email
```

**Better-Auth:**
```typescript
session.user.name
session.user.email
```

### Authentication Methods
**Supabase:**
```typescript
await supabase.auth.signInWithPassword({ email, password })
await supabase.auth.signUp({ email, password, options: { data: { name } } })
await supabase.auth.signOut()
```

**Better-Auth:**
```typescript
await signIn.email({ email, password })
await signUp.email({ email, password, name })
await signOut()
```

### Server-Side Session
**Supabase:**
```typescript
const { data: { session } } = await supabase.auth.getSession()
```

**Better-Auth:**
```typescript
const session = await auth.api.getSession({ headers: await headers() })
```

## Benefits of Migration

1. **Better TypeScript Support**: Full type safety across client and server
2. **Framework Agnostic**: Not tied to any specific backend service
3. **Flexible Configuration**: Easy to customize and extend
4. **Plugin System**: Rich ecosystem of plugins for additional features
5. **Better Developer Experience**: Cleaner APIs and better error handling
6. **Reduced Vendor Lock-in**: Own your authentication logic

## Troubleshooting

### Common Issues:

1. **Database Connection**: Ensure DATABASE_URL is correctly set
2. **Secret Key**: Generate a strong BETTER_AUTH_SECRET
3. **Schema Generation**: Run `npm run auth:generate` if tables are missing
4. **Session Cookies**: Clear browser cookies if experiencing login issues

### Migration Checklist:

- [ ] Dependencies installed
- [ ] Environment variables updated
- [ ] Database schema generated
- [ ] Database migrations run
- [ ] Application starts without errors
- [ ] Login functionality works
- [ ] Registration functionality works
- [ ] Protected routes work correctly
- [ ] Sign out functionality works

## Next Steps

After successful migration, consider:

1. **Email Verification**: Enable email verification in auth config
2. **Social Providers**: Add OAuth providers (Google, GitHub, etc.)
3. **Two-Factor Authentication**: Add 2FA plugin
4. **Rate Limiting**: Implement rate limiting for auth endpoints
5. **Session Management**: Configure session expiration and refresh

## Support

If you encounter issues during migration:

1. Check the Better-Auth documentation: https://better-auth.com
2. Review the migration checklist above
3. Ensure all environment variables are correctly set
4. Verify database connectivity and schema generation
