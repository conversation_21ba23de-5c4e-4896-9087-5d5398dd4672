import { createServerClient } from '@/lib/supabase-server'
import { notFound } from 'next/navigation'
import { ProjectDetailClient } from '@/components/projects/project-detail-client'

interface ProjectDetailPageProps {
  params: {
    id: string
  }
}

export default async function ProjectDetailPage({ params }: ProjectDetailPageProps) {
  const supabase = createServerClient()

  // Initialize default values
  let project: any = null
  let materials: any[] = []
  let optimizationResults: any[] = []

  // Get project with pieces (only if Supabase is configured)
  if (supabase) {
    try {
      const { data: projectData, error } = await supabase
        .from('projects')
        .select(`
          *,
          pieces(*)
        `)
        .eq('id', params.id)
        .single()

      if (error || !projectData) {
        notFound()
      }

      project = projectData

      // Get user's materials for optimization
      const { data: materialsData } = await supabase
        .from('materials')
        .select('*')
        .order('name')

      // Get recent optimization results for this project
      const { data: optimizationData } = await supabase
        .from('optimization_results')
        .select(`
          *,
          sheets(
            *,
            placed_pieces(*)
          )
        `)
        .eq('project_id', params.id)
        .order('created_at', { ascending: false })
        .limit(1)

      materials = materialsData || []
      optimizationResults = optimizationData || []
    } catch (err) {
      console.error('Error fetching project data:', err)
      notFound()
    }
  } else {
    // For development without Supabase, create mock data
    project = {
      id: params.id,
      name: 'Sample Project',
      description: 'This is a sample project for development',
      saw_kerf: 3,
      kerf_unit: 'MM',
      pieces: []
    }
  }

  return (
    <ProjectDetailClient
      project={project}
      materials={materials || []}
      initialOptimizationResult={optimizationResults?.[0] || null}
    />
  )
}
