'use client'

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { FileText, Download, Zap, Package } from 'lucide-react'
import { formatNumber } from '@/lib/utils'

interface CuttingListViewProps {
  project: {
    id: string
    name: string
    description: string | null
    saw_kerf: number
    kerf_unit: string
    pieces: Array<{
      id: string
      name: string
      length: number
      width: number
      thickness: number | null
      unit: string
      quantity: number
      grain_direction: string
      priority: number
      notes: string | null
    }>
  }
  optimizationResult: any
}

export function CuttingListView({ project, optimizationResult }: CuttingListViewProps) {
  const handleExportText = () => {
    if (!optimizationResult?.layouts) return

    let content = `${project.name} - Cutting Plan\n`
    content += `Generated: ${new Date().toLocaleDateString()}\n`
    content += `Total Sheets: ${optimizationResult.layouts.length}\n`
    content += `Efficiency: ${optimizationResult.metadata?.efficiency?.toFixed(1) || 'N/A'}%\n\n`

    content += `PIECES LIST:\n`
    content += `============\n`
    project.pieces.forEach((piece, index) => {
      content += `${index + 1}. ${piece.name}\n`
      content += `   Dimensions: ${piece.length} × ${piece.width}`
      if (piece.thickness) content += ` × ${piece.thickness}`
      content += ` ${piece.unit}\n`
      content += `   Quantity: ${piece.quantity}\n`
      if (piece.grain_direction !== 'NONE') {
        content += `   Grain Direction: ${piece.grain_direction}\n`
      }
      if (piece.notes) {
        content += `   Notes: ${piece.notes}\n`
      }
      content += `\n`
    })

    content += `\nCUTTING PLAN:\n`
    content += `=============\n`
    optimizationResult.layouts.forEach((layout: any, sheetIndex: number) => {
      content += `\nSheet ${sheetIndex + 1}: ${layout.baseMaterial.name}\n`
      content += `Material: ${formatNumber(layout.widthUsed)} × ${formatNumber(layout.heightUsed)} ${layout.baseMaterial.unit}\n`
      content += `Efficiency: ${layout.efficiency.toFixed(1)}%\n`
      content += `Pieces on this sheet:\n`
      
      layout.pieces.forEach((piece: any, pieceIndex: number) => {
        content += `  ${pieceIndex + 1}. ${piece.name} - ${formatNumber(piece.packedWidth)} × ${formatNumber(piece.packedHeight)} ${piece.unit}`
        if (piece.rotation > 0) {
          content += ` (rotated ${piece.rotation}°)`
        }
        content += `\n`
      })
    })

    // Download as text file
    const blob = new Blob([content], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${project.name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_cutting_plan.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  if (!optimizationResult?.layouts || optimizationResult.layouts.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-16">
          <FileText className="h-16 w-16 text-gray-400 mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">No cutting plan generated</h3>
          <p className="text-gray-600 text-center mb-6 max-w-md">
            Run the optimization algorithm to generate a cutting plan and view the detailed cutting list.
          </p>
          <Alert>
            <Zap className="h-4 w-4" />
            <AlertDescription>
              Switch to the "Pieces" tab to add pieces, then click "Optimize" to generate your cutting plan.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Summary */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Cutting Plan Summary</CardTitle>
              <CardDescription>
                Optimized cutting plan for {project.name}
              </CardDescription>
            </div>
            <Button onClick={handleExportText}>
              <Download className="h-4 w-4 mr-2" />
              Export Text
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {optimizationResult.layouts.length}
              </div>
              <div className="text-sm text-gray-600">Total Sheets</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {optimizationResult.metadata?.efficiency?.toFixed(1) || 'N/A'}%
              </div>
              <div className="text-sm text-gray-600">Efficiency</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {formatNumber(optimizationResult.metadata?.totalWaste || 0)}
              </div>
              <div className="text-sm text-gray-600">Total Waste</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {optimizationResult.metadata?.processingTime || 'N/A'}ms
              </div>
              <div className="text-sm text-gray-600">Processing Time</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Pieces List */}
      <Card>
        <CardHeader>
          <CardTitle>Pieces Required</CardTitle>
          <CardDescription>
            All pieces that need to be cut for this project
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {project.pieces.map((piece, index) => (
              <div key={piece.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center space-x-3">
                    <span className="text-sm font-medium text-gray-500">#{index + 1}</span>
                    <div>
                      <h4 className="font-medium">{piece.name}</h4>
                      <div className="text-sm text-gray-600">
                        {formatNumber(piece.length)} × {formatNumber(piece.width)}
                        {piece.thickness && ` × ${formatNumber(piece.thickness)}`} {piece.unit}
                      </div>
                    </div>
                  </div>
                  {piece.notes && (
                    <div className="text-xs text-gray-500 mt-1 ml-8">{piece.notes}</div>
                  )}
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium">Qty: {piece.quantity}</div>
                  {piece.grain_direction !== 'NONE' && (
                    <div className="text-xs text-blue-600">
                      Grain: {piece.grain_direction.toLowerCase()}
                    </div>
                  )}
                  <div className="text-xs text-gray-500">Priority: {piece.priority}</div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Sheet-by-Sheet Breakdown */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Sheet-by-Sheet Cutting Plan</h3>
        {optimizationResult.layouts.map((layout: any, sheetIndex: number) => (
          <Card key={sheetIndex}>
            <CardHeader>
              <CardTitle className="text-lg">
                Sheet {sheetIndex + 1}: {layout.baseMaterial.name}
              </CardTitle>
              <CardDescription>
                Material: {formatNumber(layout.widthUsed)} × {formatNumber(layout.heightUsed)} {layout.baseMaterial.unit} • 
                Efficiency: {layout.efficiency.toFixed(1)}% • 
                Waste: {formatNumber(layout.wasteArea)} {layout.baseMaterial.unit}²
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {layout.pieces.map((piece: any, pieceIndex: number) => (
                  <div key={pieceIndex} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <div className="flex items-center space-x-3">
                      <span className="text-sm font-medium text-gray-500">
                        {pieceIndex + 1}.
                      </span>
                      <div>
                        <span className="font-medium">{piece.name}</span>
                        <div className="text-sm text-gray-600">
                          {formatNumber(piece.packedWidth)} × {formatNumber(piece.packedHeight)} {piece.unit}
                          {piece.rotation > 0 && (
                            <span className="text-blue-600 ml-2">(rotated {piece.rotation}°)</span>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="text-sm text-gray-500">
                      Position: ({formatNumber(piece.x)}, {formatNumber(piece.y)})
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
