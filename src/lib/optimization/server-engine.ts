/**
 * SERVER-SIDE OPTIMIZATION ENGINE
 * 
 * This file contains the proprietary optimization algorithms that should NEVER
 * be exposed to the client-side. This is the intellectual property that needs
 * to be protected.
 * 
 * SECURITY NOTE: This file should only run on the server and never be bundled
 * with client-side code.
 */

import { convertToUnit, generateId, getPieceColor } from '@/lib/utils'

export interface Material {
  id: string
  name: string
  length: number
  width: number
  thickness?: number
  unit: 'mm' | 'cm' | 'm' | 'in' | 'ft'
  quantity: number
  cost?: number
}

export interface Piece {
  id: string
  name: string
  length: number
  width: number
  thickness?: number
  unit: 'mm' | 'cm' | 'm' | 'in' | 'ft'
  quantity: number
  grainDirection?: 'NONE' | 'HORIZONTAL' | 'VERTICAL' | 'EITHER'
  priority?: number
}

export interface PlacedPiece extends Piece {
  x: number
  y: number
  packedWidth: number
  packedHeight: number
  rotation: number
  color: string
  sheetId: string
}

export interface OptimizedLayout {
  sheetId: string
  baseMaterial: Material
  pieces: PlacedPiece[]
  widthUsed: number
  heightUsed: number
  wasteArea: number
  efficiency: number
}

export interface OptimizationRequest {
  materials: Material[]
  pieces: Piece[]
  sawKerf: number
  kerfUnit: 'mm' | 'cm' | 'm' | 'in' | 'ft'
  projectId: string
  userId?: string
}

export interface OptimizationResponse {
  success: boolean
  layouts: OptimizedLayout[]
  metadata?: {
    totalSheets: number
    totalWaste: number
    efficiency: number
    processingTime: number
  }
  error?: string
}

interface PieceForPacking extends Piece {
  packedWidth: number
  packedHeight: number
  packed: boolean
  originalIndex: number
  uniqueId: string
}

export class OptimizationEngine {
  private readonly BASE_UNIT = 'mm'

  /**
   * PROPRIETARY: Main optimization method
   * This contains the core intellectual property
   */
  async optimize(request: OptimizationRequest): Promise<OptimizationResponse> {
    const startTime = Date.now()

    try {
      // Validate input
      this.validateOptimizationRequest(request)

      // Prepare pieces for packing
      const piecesToPack = this.preparePiecesForPacking(
        request.pieces,
        request.sawKerf,
        request.kerfUnit
      )

      // Check for oversized pieces
      this.validatePieceSizes(piecesToPack, request.materials, request.sawKerf, request.kerfUnit)

      // PROPRIETARY: Perform advanced optimization
      const layouts = await this.performAdvancedOptimization(
        request.materials,
        piecesToPack,
        request.sawKerf,
        request.kerfUnit
      )

      // Calculate metadata
      const metadata = this.calculateOptimizationMetadata(layouts, startTime)

      return {
        success: true,
        layouts,
        metadata,
      }
    } catch (error) {
      return {
        success: false,
        layouts: [],
        error: error instanceof Error ? error.message : 'Unknown optimization error',
      }
    }
  }

  /**
   * PROPRIETARY: Advanced multi-strategy optimization
   * This is the core IP that provides competitive advantage
   */
  private async performAdvancedOptimization(
    materials: Material[],
    pieces: PieceForPacking[],
    sawKerf: number,
    kerfUnit: string
  ): Promise<OptimizedLayout[]> {
    const kerfInBaseUnit = convertToUnit(sawKerf, kerfUnit as any, this.BASE_UNIT)

    // PROPRIETARY: Advanced piece sorting with multiple criteria
    const sortedPieces = this.advancedPieceSorting(pieces)

    // PROPRIETARY: Try multiple optimization strategies
    const strategies = [
      'bottom-left-fill-advanced',
      'best-fit-decreasing-optimized',
      'guillotine-split-enhanced',
      'shelf-algorithm-improved',
      'genetic-algorithm-hybrid',
    ]

    let bestLayouts: OptimizedLayout[] = []
    let bestEfficiency = 0

    for (const strategy of strategies) {
      const strategyLayouts = await this.executeOptimizationStrategy(
        materials,
        [...sortedPieces], // Clone array for each strategy
        kerfInBaseUnit,
        strategy
      )

      const efficiency = this.calculateOverallEfficiency(strategyLayouts)
      
      if (efficiency > bestEfficiency || bestLayouts.length === 0) {
        bestLayouts = strategyLayouts
        bestEfficiency = efficiency
      }
    }

    return bestLayouts
  }

  /**
   * PROPRIETARY: Advanced piece sorting algorithm
   * Uses multiple criteria including area, aspect ratio, and priority
   */
  private advancedPieceSorting(pieces: PieceForPacking[]): PieceForPacking[] {
    return pieces.sort((a, b) => {
      // Priority first (higher priority first)
      const priorityDiff = (b.priority || 1) - (a.priority || 1)
      if (priorityDiff !== 0) return priorityDiff

      // Area second (larger pieces first)
      const areaA = a.packedWidth * a.packedHeight
      const areaB = b.packedWidth * b.packedHeight
      const areaDiff = areaB - areaA
      if (Math.abs(areaDiff) > 0.01) return areaDiff

      // Aspect ratio third (more square pieces first for better packing)
      const aspectA = Math.max(a.packedWidth, a.packedHeight) / Math.min(a.packedWidth, a.packedHeight)
      const aspectB = Math.max(b.packedWidth, b.packedHeight) / Math.min(b.packedWidth, b.packedHeight)
      
      return aspectA - aspectB
    })
  }

  /**
   * PROPRIETARY: Execute specific optimization strategy
   */
  private async executeOptimizationStrategy(
    materials: Material[],
    pieces: PieceForPacking[],
    kerfInBaseUnit: number,
    strategy: string
  ): Promise<OptimizedLayout[]> {
    const layouts: OptimizedLayout[] = []

    // Reset packed status
    pieces.forEach(piece => piece.packed = false)

    for (const materialTemplate of materials) {
      for (let i = 0; i < materialTemplate.quantity; i++) {
        if (pieces.every(p => p.packed)) break

        const sheetId = `sheet-${materialTemplate.id}-${i}`
        const materialInstance: Material = {
          ...materialTemplate,
          id: sheetId,
          quantity: 1,
        }

        const layout = await this.packSheet(
          materialInstance,
          pieces.filter(p => !p.packed),
          kerfInBaseUnit,
          strategy
        )

        if (layout && layout.pieces.length > 0) {
          layouts.push(layout)
        }
      }

      if (pieces.every(p => p.packed)) break
    }

    return layouts
  }

  /**
   * PROPRIETARY: Advanced sheet packing algorithm
   */
  private async packSheet(
    material: Material,
    availablePieces: PieceForPacking[],
    kerfInBaseUnit: number,
    strategy: string
  ): Promise<OptimizedLayout | null> {
    const binWidth = convertToUnit(material.width, material.unit as any, this.BASE_UNIT)
    const binHeight = convertToUnit(material.length, material.unit as any, this.BASE_UNIT)

    const placedPieces: PlacedPiece[] = []
    const occupiedRectangles: Array<{ x: number, y: number, width: number, height: number }> = []

    // PROPRIETARY: Strategy-specific packing logic
    switch (strategy) {
      case 'bottom-left-fill-advanced':
        this.bottomLeftFillAdvanced(availablePieces, binWidth, binHeight, kerfInBaseUnit, placedPieces, occupiedRectangles)
        break
      
      case 'best-fit-decreasing-optimized':
        this.bestFitDecreasingOptimized(availablePieces, binWidth, binHeight, kerfInBaseUnit, placedPieces, occupiedRectangles)
        break
      
      case 'guillotine-split-enhanced':
        this.guillotineSplitEnhanced(availablePieces, binWidth, binHeight, kerfInBaseUnit, placedPieces, occupiedRectangles)
        break
      
      case 'shelf-algorithm-improved':
        this.shelfAlgorithmImproved(availablePieces, binWidth, binHeight, kerfInBaseUnit, placedPieces, occupiedRectangles)
        break
      
      case 'genetic-algorithm-hybrid':
        await this.geneticAlgorithmHybrid(availablePieces, binWidth, binHeight, kerfInBaseUnit, placedPieces, occupiedRectangles)
        break
      
      default:
        // Fallback to basic algorithm
        this.basicBottomLeftFill(availablePieces, binWidth, binHeight, kerfInBaseUnit, placedPieces, occupiedRectangles)
    }

    if (placedPieces.length === 0) return null

    // Mark pieces as packed
    placedPieces.forEach(placedPiece => {
      const originalPiece = availablePieces.find(p => p.uniqueId === placedPiece.id)
      if (originalPiece) {
        originalPiece.packed = true
      }
    })

    // Calculate waste and efficiency
    const usedArea = placedPieces.reduce((sum, piece) => sum + (piece.packedWidth * piece.packedHeight), 0)
    const totalArea = binWidth * binHeight
    const wasteArea = totalArea - usedArea
    const efficiency = (usedArea / totalArea) * 100

    return {
      sheetId: material.id,
      baseMaterial: material,
      pieces: placedPieces,
      widthUsed: binWidth,
      heightUsed: binHeight,
      wasteArea,
      efficiency,
    }
  }

  /**
   * PROPRIETARY: Bottom-left fill with advanced heuristics
   */
  private bottomLeftFillAdvanced(
    pieces: PieceForPacking[],
    binWidth: number,
    binHeight: number,
    kerf: number,
    placedPieces: PlacedPiece[],
    occupiedRectangles: Array<{ x: number, y: number, width: number, height: number }>
  ): void {
    for (const piece of pieces) {
      if (piece.packed) continue

      const bestPosition = this.findBestPositionAdvanced(
        piece,
        binWidth,
        binHeight,
        kerf,
        occupiedRectangles
      )

      if (bestPosition) {
        const placedPiece: PlacedPiece = {
          ...piece,
          x: bestPosition.x,
          y: bestPosition.y,
          packedWidth: bestPosition.width,
          packedHeight: bestPosition.height,
          rotation: bestPosition.rotation,
          color: getPieceColor(piece.originalIndex),
          sheetId: '',
        }

        placedPieces.push(placedPiece)
        occupiedRectangles.push({
          x: bestPosition.x,
          y: bestPosition.y,
          width: bestPosition.width + kerf,
          height: bestPosition.height + kerf,
        })
      }
    }
  }

  /**
   * PROPRIETARY: Find best position with advanced scoring
   */
  private findBestPositionAdvanced(
    piece: PieceForPacking,
    binWidth: number,
    binHeight: number,
    kerf: number,
    occupiedRectangles: Array<{ x: number, y: number, width: number, height: number }>
  ): { x: number, y: number, width: number, height: number, rotation: number } | null {
    let bestPosition: any = null
    let bestScore = -1

    // Try both orientations
    const orientations = [
      { width: piece.packedWidth, height: piece.packedHeight, rotation: 0 },
      { width: piece.packedHeight, height: piece.packedWidth, rotation: 90 },
    ]

    for (const orientation of orientations) {
      // Skip if piece doesn't fit in this orientation
      if (orientation.width + kerf > binWidth || orientation.height + kerf > binHeight) {
        continue
      }

      // Try different positions
      for (let y = 0; y <= binHeight - orientation.height - kerf; y += 1) {
        for (let x = 0; x <= binWidth - orientation.width - kerf; x += 1) {
          if (this.canPlacePiece(x, y, orientation.width, orientation.height, kerf, occupiedRectangles)) {
            const score = this.calculatePositionScore(x, y, orientation.width, orientation.height, binWidth, binHeight)
            
            if (score > bestScore) {
              bestScore = score
              bestPosition = {
                x,
                y,
                width: orientation.width,
                height: orientation.height,
                rotation: orientation.rotation,
              }
            }
          }
        }
      }
    }

    return bestPosition
  }

  /**
   * PROPRIETARY: Advanced position scoring algorithm
   */
  private calculatePositionScore(
    x: number,
    y: number,
    width: number,
    height: number,
    binWidth: number,
    binHeight: number
  ): number {
    // Prefer bottom-left positions
    const bottomLeftScore = (binHeight - y - height) + (binWidth - x - width)
    
    // Prefer positions that create less fragmentation
    const fragmentationScore = this.calculateFragmentationScore(x, y, width, height, binWidth, binHeight)
    
    // Combine scores with weights
    return bottomLeftScore * 0.7 + fragmentationScore * 0.3
  }

  /**
   * PROPRIETARY: Calculate fragmentation score
   */
  private calculateFragmentationScore(
    x: number,
    y: number,
    width: number,
    height: number,
    binWidth: number,
    binHeight: number
  ): number {
    // This is a simplified fragmentation calculation
    // In a real implementation, this would be much more sophisticated
    const rightSpace = binWidth - (x + width)
    const topSpace = binHeight - (y + height)
    
    return Math.min(rightSpace, topSpace)
  }

  // Additional proprietary methods would be implemented here...
  // For brevity, I'm including stubs for the other algorithms

  private bestFitDecreasingOptimized(pieces: any[], binWidth: number, binHeight: number, kerf: number, placedPieces: any[], occupiedRectangles: any[]): void {
    // PROPRIETARY: Best-fit decreasing with optimizations
    this.basicBottomLeftFill(pieces, binWidth, binHeight, kerf, placedPieces, occupiedRectangles)
  }

  private guillotineSplitEnhanced(pieces: any[], binWidth: number, binHeight: number, kerf: number, placedPieces: any[], occupiedRectangles: any[]): void {
    // PROPRIETARY: Enhanced guillotine split algorithm
    this.basicBottomLeftFill(pieces, binWidth, binHeight, kerf, placedPieces, occupiedRectangles)
  }

  private shelfAlgorithmImproved(pieces: any[], binWidth: number, binHeight: number, kerf: number, placedPieces: any[], occupiedRectangles: any[]): void {
    // PROPRIETARY: Improved shelf algorithm
    this.basicBottomLeftFill(pieces, binWidth, binHeight, kerf, placedPieces, occupiedRectangles)
  }

  private async geneticAlgorithmHybrid(pieces: any[], binWidth: number, binHeight: number, kerf: number, placedPieces: any[], occupiedRectangles: any[]): Promise<void> {
    // PROPRIETARY: Genetic algorithm hybrid approach
    this.basicBottomLeftFill(pieces, binWidth, binHeight, kerf, placedPieces, occupiedRectangles)
  }

  /**
   * Basic bottom-left fill (fallback algorithm)
   */
  private basicBottomLeftFill(
    pieces: PieceForPacking[],
    binWidth: number,
    binHeight: number,
    kerf: number,
    placedPieces: PlacedPiece[],
    occupiedRectangles: Array<{ x: number, y: number, width: number, height: number }>
  ): void {
    for (const piece of pieces) {
      if (piece.packed) continue

      // Try to place piece
      let placed = false
      
      for (let y = 0; y <= binHeight - piece.packedHeight - kerf && !placed; y++) {
        for (let x = 0; x <= binWidth - piece.packedWidth - kerf && !placed; x++) {
          if (this.canPlacePiece(x, y, piece.packedWidth, piece.packedHeight, kerf, occupiedRectangles)) {
            const placedPiece: PlacedPiece = {
              ...piece,
              x,
              y,
              packedWidth: piece.packedWidth,
              packedHeight: piece.packedHeight,
              rotation: 0,
              color: getPieceColor(piece.originalIndex),
              sheetId: '',
            }

            placedPieces.push(placedPiece)
            occupiedRectangles.push({
              x,
              y,
              width: piece.packedWidth + kerf,
              height: piece.packedHeight + kerf,
            })
            placed = true
          }
        }
      }
    }
  }

  /**
   * Check if piece can be placed at position
   */
  private canPlacePiece(
    x: number,
    y: number,
    width: number,
    height: number,
    kerf: number,
    occupiedRectangles: Array<{ x: number, y: number, width: number, height: number }>
  ): boolean {
    const pieceRect = {
      x,
      y,
      width: width + kerf,
      height: height + kerf,
    }

    return !occupiedRectangles.some(rect => this.rectanglesOverlap(pieceRect, rect))
  }

  /**
   * Check if two rectangles overlap
   */
  private rectanglesOverlap(rect1: any, rect2: any): boolean {
    return !(
      rect1.x + rect1.width <= rect2.x ||
      rect2.x + rect2.width <= rect1.x ||
      rect1.y + rect1.height <= rect2.y ||
      rect2.y + rect2.height <= rect1.y
    )
  }

  /**
   * Prepare pieces for packing
   */
  private preparePiecesForPacking(
    pieces: Piece[],
    sawKerf: number,
    kerfUnit: string
  ): PieceForPacking[] {
    const kerfInBaseUnit = convertToUnit(sawKerf, kerfUnit as any, this.BASE_UNIT)
    const piecesToPack: PieceForPacking[] = []

    pieces.forEach((piece, index) => {
      const pieceWidthInBaseUnit = convertToUnit(piece.width, piece.unit as any, this.BASE_UNIT)
      const pieceLengthInBaseUnit = convertToUnit(piece.length, piece.unit as any, this.BASE_UNIT)

      for (let i = 0; i < piece.quantity; i++) {
        piecesToPack.push({
          ...piece,
          uniqueId: `${piece.id}-${i}`,
          packedWidth: pieceWidthInBaseUnit,
          packedHeight: pieceLengthInBaseUnit,
          packed: false,
          originalIndex: index,
        })
      }
    })

    return piecesToPack
  }

  /**
   * Validate optimization request
   */
  private validateOptimizationRequest(request: OptimizationRequest): void {
    if (!request.materials || request.materials.length === 0) {
      throw new Error('No materials provided')
    }

    if (!request.pieces || request.pieces.length === 0) {
      throw new Error('No pieces provided')
    }

    if (request.sawKerf < 0) {
      throw new Error('Saw kerf cannot be negative')
    }
  }

  /**
   * Validate piece sizes against materials
   */
  private validatePieceSizes(
    pieces: PieceForPacking[],
    materials: Material[],
    sawKerf: number,
    kerfUnit: string
  ): void {
    const kerfInBaseUnit = convertToUnit(sawKerf, kerfUnit as any, this.BASE_UNIT)

    for (const piece of pieces) {
      const canFitInAnyMaterial = materials.some(material => {
        const materialWidth = convertToUnit(material.width, material.unit as any, this.BASE_UNIT)
        const materialHeight = convertToUnit(material.length, material.unit as any, this.BASE_UNIT)

        return (
          (piece.packedWidth + kerfInBaseUnit <= materialWidth && piece.packedHeight + kerfInBaseUnit <= materialHeight) ||
          (piece.packedHeight + kerfInBaseUnit <= materialWidth && piece.packedWidth + kerfInBaseUnit <= materialHeight)
        )
      })

      if (!canFitInAnyMaterial) {
        throw new Error(`Piece "${piece.name}" is too large to fit in any available material`)
      }
    }
  }

  /**
   * Calculate overall efficiency
   */
  private calculateOverallEfficiency(layouts: OptimizedLayout[]): number {
    if (layouts.length === 0) return 0

    const totalUsedArea = layouts.reduce((sum, layout) => {
      const usedArea = layout.pieces.reduce((pieceSum, piece) => 
        pieceSum + (piece.packedWidth * piece.packedHeight), 0)
      return sum + usedArea
    }, 0)

    const totalArea = layouts.reduce((sum, layout) => 
      sum + (layout.widthUsed * layout.heightUsed), 0)

    return totalArea > 0 ? (totalUsedArea / totalArea) * 100 : 0
  }

  /**
   * Calculate optimization metadata
   */
  private calculateOptimizationMetadata(layouts: OptimizedLayout[], startTime: number): any {
    const totalWaste = layouts.reduce((sum, layout) => sum + layout.wasteArea, 0)
    const efficiency = this.calculateOverallEfficiency(layouts)
    const processingTime = Date.now() - startTime

    return {
      totalSheets: layouts.length,
      totalWaste,
      efficiency,
      processingTime,
    }
  }
}
